import 'dart:async';
import 'dart:io';

import 'package:bot_toast/bot_toast.dart';
import 'package:diogeneschatbot/features/offline_image/presentation/pages/index/view.dart';
import 'package:diogeneschatbot/models/usage.dart';
import 'package:diogeneschatbot/pages/chathistory_page.dart';
import 'package:diogeneschatbot/theme/app_theme.dart';
import 'package:diogeneschatbot/util/util.dart';
import 'package:diogeneschatbot/util/util_token.dart';
import 'package:diogeneschatbot/utils/image_utils.dart';
import 'package:diogeneschatbot/utils/logger.dart';
import 'package:diogeneschatbot/widgets/chat_message_widgets.dart';
import 'package:diogeneschatbot/widgets/enhanced_app_bar.dart';
import 'package:diogeneschatbot/widgets/enhanced_button.dart';
import 'package:diogeneschatbot/widgets/enhanced_card.dart';
import 'package:diogeneschatbot/widgets/enhanced_loading.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';

/// Enhanced image generation page for creating AI-generated images
/// Provides comprehensive image generation, editing, and variation capabilities
class GenerateImagePage extends StatefulWidget {
  /// Page title displayed in the app bar
  final String title;

  /// Usage type for tracking and billing purposes
  final Usage type;

  /// Current user ID for authentication and tracking
  final String currentUserId;

  const GenerateImagePage({
    super.key,
    required this.title,
    required this.type,
    required this.currentUserId,
  });

  @override
  State<GenerateImagePage> createState() => _GenerateImagePageState();
}

/// Available image operations for enhanced functionality
enum ImageOperation {
  /// Edit an existing image with AI
  editImage,

  /// Create variations of an existing image
  createVariations,
}

class _GenerateImagePageState extends State<GenerateImagePage> {
  // Core state
  String _inputText = '';
  String _imageUrl = '';
  DateTime _requestDateTime = DateTime.fromMillisecondsSinceEpoch(0);

  // Counters and metrics
  int _wordCount = 0;
  int _letterCount = 0;

  // Image handling
  File? _image;
  File? _mask;
  final ImagePicker _picker = ImagePicker();

  // Chat and UI state
  List<Widget> _chatMessages = [];
  ImageOperation? _selectedOperation;
  bool _isLoading = false;
  bool _isGenerating = false;

  // Controllers and streams
  final TextEditingController _textController = TextEditingController();
  final StreamController<List<Widget>> _chatStreamController =
      StreamController<List<Widget>>.broadcast();

  /// Get the usage type from widget
  Usage get usageType => widget.type;

  @override
  void initState() {
    super.initState();
    _chatStreamController.add(_chatMessages);
  }

  @override
  void dispose() {
    _chatStreamController.close();
    super.dispose();
  }

  /// Picks an image from the gallery for editing or variations
  Future<void> _pickImage() async {
    final pickedFile = await _picker.pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      final File? processedImage = await ImageUtils.processImage(
        File(pickedFile.path),
      );
      if (processedImage != null) {
        setState(() {
          _image = processedImage;
        });
      } else {
        logger.d('Invalid image.');
      }
    } else {
      logger.d('No image selected.');
    }
  }

  Future<void> _editImage() async {
    if (_image == null) {
      _showSnackBar('Please upload an image first.');
      return;
    }
    setState(() {
      _isLoading = true;
    });
    final editedImageUrl = await Util.editImage(
      _image!,
      _mask,
      _textController.text,
      widget.currentUserId,
      usageType,
      widget.currentUserId,
    );
    setState(() {
      _imageUrl = editedImageUrl;
      _isLoading = false;
      _requestDateTime = DateTime.now().toUtc();
      _updateChatMessages();
    });
  }

  Future<void> _createVariations() async {
    if (_image == null) {
      _showSnackBar('Please upload an image first.');
      return;
    }
    setState(() {
      _isLoading = true;
    });
    final variations = await Util.createImageVariations(
      _image!,
      widget.currentUserId,
      usageType,
      widget.currentUserId,
    );
    setState(() {
      _imageUrl = variations;
      _isLoading = false;
      _requestDateTime = DateTime.now().toUtc();
      _updateChatMessages();
    });
  }

  Future<void> _sendMessage(String message, Usage usageType) async {
    BotToast.showText(
      text: "Please be patient while image generation is in progress...",
      duration: Duration(seconds: 3),
    ); // Show explicit message

    BotToast.showLoading(); // Show loading toast
    setState(() {
      _isLoading = true;
    });
    final String inputText = _textController.text;
    final response = await Util.CallChatAPI(
      inputText,
      usageType,
      <Map<String, String>>[],
      widget.currentUserId,
      widget.currentUserId,
    );
    final imageUrlRegExp = RegExp(
      r'^https?:\/\/[\w-]+(\.[\w-]+)+([\w.,@?^=%&:/~+#-]*[\w@?^=%&/~+#-])?\.(jpg|jpeg|png|gif|bmp)(\?.*)?$',
    );
    final isValidImageUrl = imageUrlRegExp.hasMatch(response.trim());
    if (isValidImageUrl) {
      setState(() {
        _inputText = inputText;
        _imageUrl = response;
        _requestDateTime = DateTime.now().toUtc();
        _isLoading = false;
        _updateChatMessages();
      });
    } else {
      logger.d('Request failed.');
      setState(() {
        _isLoading = false;
      });
    }
    BotToast.closeAllLoading(); // Show loading toast
  }

  List<Widget> _createChatMessage() {
    return <Widget>[
      if (_requestDateTime != DateTime.fromMillisecondsSinceEpoch(0)) ...[
        Column(
          children: [
            ChatMessageHeader(requestDateTime: _requestDateTime),
            ChatMessageBody(inputText: _inputText),
            ChatMessageImage(imageUrl: _imageUrl),
            ChatMessageActions(
              imageUrl: _imageUrl,
              inputText: _inputText,
              currentUserId: widget.currentUserId,
            ),
          ],
        ),
      ],
    ];
  }

  void _updateChatMessages() {
    setState(() {
      _chatMessages.addAll(_createChatMessage());
    });
    _chatStreamController.sink.add(_chatMessages);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDark ? AppTheme.darkSurface : AppTheme.lightSurface,
      appBar: AppBarStyles.primary(
        title: 'AI Image Generator',
        actions: [
          // Chat History
          IconButton(
            key: Key("ChatHistoryButtonOnChatPage"),
            icon: Icon(Icons.history, color: Colors.white),
            tooltip: 'Image Generation History',
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => ChatHistoryPage(usage: usageType),
                ),
              );
            },
          ),
          // Offline Generation (iOS/macOS only)
          if (!kIsWeb && (Platform.isIOS || Platform.isMacOS))
            IconButton(
              key: Key("GenerateImageButtonOfflinePage"),
              icon: Icon(Icons.offline_bolt_outlined, color: Colors.white),
              tooltip: 'Offline Image Generation',
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => OfflineImageGenerationPage(),
                  ),
                );
              },
            ),
        ],
      ),
      body: Column(
        children: <Widget>[
          // Enhanced chat area
          Expanded(
            child: _chatMessages.isEmpty
                ? _buildEmptyState(context, theme)
                : StreamBuilder(
                    stream: _chatStreamController.stream,
                    initialData: _chatMessages,
                    builder: (BuildContext context, AsyncSnapshot snapshot) {
                      return ListView.builder(
                        padding: const EdgeInsets.all(16),
                        itemCount: snapshot.data.length,
                        reverse: true,
                        itemBuilder: (BuildContext context, int index) {
                          return snapshot.data[snapshot.data.length -
                              1 -
                              index];
                        },
                      );
                    },
                  ),
          ),

          // Enhanced image thumbnails and controls
          if (_image != null || _mask != null) _buildEnhancedImageThumbnails(),

          // Enhanced input area
          _buildEnhancedInputArea(context, theme, isDark),
        ],
      ),
    );
  }

  Widget _buildImageThumbnails() {
    return Container(
      margin: EdgeInsets.all(16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          _image != null
              ? Image.file(_image!, width: 50, height: 50, fit: BoxFit.cover)
              : SizedBox.shrink(),
          _mask != null
              ? Image.file(_mask!, width: 50, height: 50, fit: BoxFit.cover)
              : SizedBox.shrink(),
        ],
      ),
    );
  }

  Widget _buildImageUploadButtons() {
    return Container(
      margin: EdgeInsets.all(12.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Flexible(
          //   child: ElevatedButton.icon(
          //     key: Key("UploadImageButton"),
          //     icon: Icon(Icons.file_upload),
          //     label: Text("Upload Image"),
          //     onPressed: () async {
          //       setState(() {
          //         _isLoading = true;
          //       });
          //       await _pickImage();
          //       setState(() {
          //         _isLoading = false;
          //       });
          //     },
          //   ),
          // ),
          SizedBox(width: 16.0),
          // Flexible(
          //   child: ElevatedButton.icon(
          //     key: Key("UploadMaskButton"),
          //     icon: Icon(Icons.masks_outlined),
          //     label: Text("Upload Mask"),
          //     onPressed: () async {
          //       setState(() {
          //         _isLoading = true;
          //       });
          //       final pickedFile =
          //           await picker.pickImage(source: ImageSource.gallery);
          //       if (pickedFile != null) {
          //         final processedMask = await _processImage(
          //             File(pickedFile.path),
          //             isMask: true);
          //         if (processedMask != null) {
          //           setState(() {
          //             _mask = processedMask;
          //           });
          //         } else {
          //           logger.d('Invalid mask image.');
          //         }
          //       } else {
          //         logger.d('No mask image selected.');
          //       }
          //       setState(() {
          //         _isLoading = false;
          //       });
          //     },
          //   ),
          // ),
        ],
      ),
    );
  }

  Widget _buildImageOperationButtons() {
    return Container(
      margin: EdgeInsets.all(12.0),
      child: Row(
        children: [
          Flexible(
            fit: FlexFit.loose,
            child: RadioListTile<ImageOperation>(
              title: const Text('Edit Image'),
              value: ImageOperation.editImage,
              groupValue: _selectedOperation,
              onChanged: (ImageOperation? value) {
                setState(() {
                  _selectedOperation = value;
                });
              },
            ),
          ),
          Flexible(
            fit: FlexFit.loose,
            child: RadioListTile<ImageOperation>(
              title: const Text('Create Variations'),
              value: ImageOperation.createVariations,
              groupValue: _selectedOperation,
              onChanged: (ImageOperation? value) {
                setState(() {
                  _selectedOperation = value;
                });
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInputTextAndSendButton() {
    return Container(
      margin: EdgeInsets.all(16.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: <Widget>[
              Flexible(
                child: TextField(
                  maxLines: null,
                  key: Key("GenerateImageInputTextField"),
                  controller: _textController,
                  onChanged: (value) {
                    setState(() {
                      _wordCount = Util_Token.countTokens(value.trim());
                      _letterCount = value.trim().length;
                    });
                  },
                  onSubmitted: (value) {
                    _sendMessage(value, usageType);
                    _textController.clear();
                  },
                  decoration: InputDecoration(
                    contentPadding: EdgeInsets.all(16.0),
                    hintText: "Enter your message",
                    border: OutlineInputBorder(),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.blueAccent),
                    ),
                  ),
                ),
              ),
              _isLoading
                  ? CircularProgressIndicator()
                  : IconButton(
                      key: Key("GenerateImageSendRequestButton"),
                      icon: Icon(Icons.send, color: Colors.blueAccent),
                      onPressed: () {
                        final message = _textController.text.trim();
                        if (message.isNotEmpty) {
                          if (_selectedOperation == ImageOperation.editImage) {
                            _editImage();
                          } else if (_selectedOperation ==
                              ImageOperation.createVariations) {
                            _createVariations();
                          } else {
                            _sendMessage(message, usageType);
                          }
                          _textController.clear();
                          setState(() {
                            _wordCount = 0;
                            _letterCount = 0;
                          });
                        }
                      },
                    ),
            ],
          ),
          Row(
            children: [
              Flexible(fit: FlexFit.loose, child: Text('Words: $_wordCount')),
              Flexible(
                fit: FlexFit.loose,
                child: Text(' Letters: $_letterCount'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(message)));
  }

  /// Builds an enhanced empty state widget with helpful messaging
  Widget _buildEmptyState(BuildContext context, ThemeData theme) {
    return Center(
      child: CardStyles.outlined(
        margin: const EdgeInsets.all(32),
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.auto_awesome,
              size: 64,
              color: AppTheme.primaryGreen.withValues(alpha: 0.6),
            ),
            const SizedBox(height: 16),
            Text(
              'AI Image Generator',
              style: theme.textTheme.headlineSmall?.copyWith(
                color: AppTheme.primaryGreen,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Describe the image you want to create and let AI bring your vision to life.',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.hintColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ButtonStyles.primary(
              text: 'Start Creating',
              icon: Icons.create,
              onPressed: () {
                // Focus on the input field
                FocusScope.of(context).requestFocus();
              },
            ),
          ],
        ),
      ),
    );
  }

  /// Builds enhanced image thumbnails with better styling
  Widget _buildEnhancedImageThumbnails() {
    return CardStyles.outlined(
      margin: const EdgeInsets.all(16.0),
      padding: const EdgeInsets.all(12.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.image, color: AppTheme.primaryGreen, size: 20),
              const SizedBox(width: 8),
              Text(
                'Selected Images',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.primaryGreen,
                ),
              ),
              const Spacer(),
              IconButton(
                icon: Icon(Icons.clear, size: 18),
                onPressed: () {
                  setState(() {
                    _image = null;
                    _mask = null;
                  });
                },
                tooltip: 'Clear images',
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              if (_image != null) ...[
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.file(
                      _image!,
                      width: 60,
                      height: 60,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  'Source Image',
                  style: TextStyle(
                    fontSize: 12,
                    color: AppTheme.primaryGreen,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
              if (_image != null && _mask != null) const SizedBox(width: 16),
              if (_mask != null) ...[
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.file(
                      _mask!,
                      width: 60,
                      height: 60,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  'Mask Image',
                  style: TextStyle(
                    fontSize: 12,
                    color: AppTheme.secondaryGreen,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  /// Builds the enhanced input area with modern styling
  Widget _buildEnhancedInputArea(
    BuildContext context,
    ThemeData theme,
    bool isDark,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: isDark ? AppTheme.darkSurface : AppTheme.lightSurface,
        border: Border(
          top: BorderSide(
            color: isDark ? AppTheme.darkOutline : AppTheme.lightOutline,
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              // Input Row
              Container(
                decoration: BoxDecoration(
                  color: theme.cardColor,
                  borderRadius: BorderRadius.circular(24),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    // Image upload button
                    Container(
                      margin: const EdgeInsets.only(left: 8),
                      child: IconButton(
                        icon: Icon(
                          Icons.add_photo_alternate,
                          color: AppTheme.primaryGreen,
                        ),
                        tooltip: 'Upload image for editing',
                        onPressed: _pickImage,
                      ),
                    ),

                    // Text input field
                    Expanded(
                      child: Container(
                        margin: const EdgeInsets.symmetric(horizontal: 8),
                        child: TextFormField(
                          maxLines: null,
                          minLines: 1,
                          key: const Key("GenerateImageInputTextField"),
                          controller: _textController,
                          onChanged: (value) {
                            setState(() {
                              _wordCount = Util_Token.countTokens(value.trim());
                              _letterCount = value.trim().length;
                            });
                          },
                          onFieldSubmitted: (value) {
                            if (value.trim().isNotEmpty) {
                              _sendMessage(value, usageType);
                              _textController.clear();
                              setState(() {
                                _wordCount = 0;
                                _letterCount = 0;
                              });
                            }
                          },
                          decoration: InputDecoration(
                            hintText:
                                "Describe the image you want to create...",
                            border: InputBorder.none,
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 12,
                            ),
                            hintStyle: TextStyle(color: theme.hintColor),
                          ),
                          style: theme.textTheme.bodyLarge,
                        ),
                      ),
                    ),

                    // Send button or loading indicator
                    Container(
                      margin: const EdgeInsets.only(right: 8),
                      child: _isLoading
                          ? Container(
                              width: 40,
                              height: 40,
                              padding: const EdgeInsets.all(8),
                              child: LoadingStyles.primary(size: 24),
                            )
                          : IconButton(
                              key: const Key("GenerateImageSendRequestButton"),
                              icon: Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: AppTheme.primaryGreen,
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.auto_awesome,
                                  color: Colors.white,
                                  size: 20,
                                ),
                              ),
                              tooltip: 'Generate image',
                              onPressed: () {
                                final message = _textController.text.trim();
                                if (message.isNotEmpty) {
                                  if (_selectedOperation ==
                                      ImageOperation.editImage) {
                                    _editImage();
                                  } else if (_selectedOperation ==
                                      ImageOperation.createVariations) {
                                    _createVariations();
                                  } else {
                                    _sendMessage(message, usageType);
                                  }
                                  _textController.clear();
                                  setState(() {
                                    _wordCount = 0;
                                    _letterCount = 0;
                                  });
                                }
                              },
                            ),
                    ),
                  ],
                ),
              ),

              // Word and character count
              if (_wordCount > 0 || _letterCount > 0)
                Container(
                  margin: const EdgeInsets.only(top: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Words: $_wordCount',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.hintColor,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Text(
                        'Characters: $_letterCount',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.hintColor,
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
