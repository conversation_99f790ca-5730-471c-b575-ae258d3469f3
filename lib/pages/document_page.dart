import 'dart:async';

import 'package:diogeneschatbot/client/opensearch_client.dart';
import 'package:diogeneschatbot/features/pdf2markdown/presentation/pages/pdf_sessions_list_page.dart';
import 'package:diogeneschatbot/models/bot.dart';
import 'package:diogeneschatbot/models/file_system_item.dart';
import 'package:diogeneschatbot/repository/firebase_storage_repository.dart';
import 'package:diogeneschatbot/theme/app_theme.dart';
import 'package:diogeneschatbot/util/util_file.dart';
import 'package:diogeneschatbot/utils/logger.dart';
import 'package:diogeneschatbot/widgets/enhanced_app_bar.dart';
import 'package:diogeneschatbot/widgets/enhanced_button.dart';
import 'package:diogeneschatbot/widgets/enhanced_card.dart';
import 'package:diogeneschatbot/widgets/enhanced_loading.dart';
import 'package:diogeneschatbot/widgets/file_selection_widget.dart';
import 'package:file_picker/file_picker.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

/// Enhanced storage page for managing files and documents
/// Provides file upload, URL input, and file management capabilities
class StoragePage extends StatefulWidget {
  /// List of preselected file system items
  final List<FileSystemItem>? preselectedItems;

  /// Optional bot ID for bot-specific file management
  final String? botId;

  /// Notifier for tracking selected files
  final ValueNotifier<Set<File>>? selectedFilesNotifier;

  const StoragePage({
    super.key,
    this.botId,
    this.preselectedItems,
    this.selectedFilesNotifier,
  });

  @override
  State<StoragePage> createState() => _StoragePageState();
}

class _StoragePageState extends State<StoragePage> {
  // Core dependencies
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseStorageRepository _firebaseStorageRepository =
      FirebaseStorageRepository();
  final FileSystemRepository _fileSystemRepository = FileSystemRepository();
  final BotRepository _botRepository = BotRepository();

  // State management
  final ValueNotifier<List<FileSystemItem>> _fileSystemItems =
      ValueNotifier<List<FileSystemItem>>([]);
  final TextEditingController _searchController = TextEditingController();

  // Current state
  String _currentPath = '';
  String _searchQuery = '';
  bool _isLoading = false;
  bool _isUploading = false;

  // Subscriptions and clients
  StreamSubscription<List<FileSystemItem>>? _knowledgeSubscription;
  late DiogenesApiClient _searchClient;

  // Configuration
  final String? serverHost =
      dotenv.env['EMBEDDING_SERVER_HOST'] ??
      const String.fromEnvironment("EMBEDDING_SERVER_HOST");

  @override
  void initState() {
    super.initState();
    _searchClient = DiogenesApiClient(baseUrl: (serverHost!));
    if (widget.botId != null) {
      _knowledgeSubscription = _botRepository.listenToKnowledgeUpdates(
        widget.botId!,
        (updatedKnowledge) {
          setState(() {
            _fileSystemItems.value = updatedKnowledge;
          });
        },
      );
    }
    _listFiles();
  }

  @override
  void dispose() {
    _knowledgeSubscription?.cancel();
    super.dispose();
  }

  Future<void> _listFiles({String? folderPath = "/"}) async {
    final userId = _auth.currentUser!.uid;

    List<FileSystemItem> items = await _fileSystemRepository.listItems(
      userId,
      folderPath ?? "/",
    );

    setState(() {
      _currentPath = folderPath ?? '/';
      _fileSystemItems.value = items;
    });
  }

  Future<void> _uploadFileOrUrl() async {
    String? chosenOption = await _showUploadOptionsDialog();

    if (chosenOption == 'uploadFile') {
      await _uploadFile();
    } else if (chosenOption == 'inputUrl') {
      String? firebaseUrl = await _inputUrl();
      if (firebaseUrl != null) {
        await _processFileOrUrl(firebaseUrl, isUrl: true);
      }
    }
  }

  /// Shows an enhanced upload options dialog with better styling
  Future<String?> _showUploadOptionsDialog() {
    return showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.cloud_upload, color: AppTheme.primaryGreen, size: 24),
            const SizedBox(width: 8),
            Text(
              'Add Content',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 20,
                color: AppTheme.primaryGreen,
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Choose how you want to add content to your collection',
              style: TextStyle(
                color: Theme.of(context).hintColor,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 24),
            _buildEnhancedDialogOption(
              text: 'Upload File',
              subtitle: 'Select files from your device',
              color: AppTheme.primaryGreen,
              returnValue: 'uploadFile',
              icon: Icons.upload_file,
            ),
            const SizedBox(height: 12),
            _buildEnhancedDialogOption(
              text: 'Add URL',
              subtitle: 'Import content from a web link',
              color: AppTheme.secondaryGreen,
              returnValue: 'inputUrl',
              icon: Icons.link,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: TextStyle(color: Theme.of(context).hintColor),
            ),
          ),
        ],
      ),
    );
  }

  /// Builds an enhanced dialog option with better styling and subtitle
  Widget _buildEnhancedDialogOption({
    required String text,
    required String subtitle,
    required Color color,
    required String returnValue,
    required IconData icon,
  }) {
    return InkWell(
      onTap: () => Navigator.pop(context, returnValue),
      borderRadius: BorderRadius.circular(12),
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: color.withValues(alpha: 0.3), width: 1.5),
          borderRadius: BorderRadius.circular(12),
          color: color.withValues(alpha: 0.05),
        ),
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 24.0),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    text,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: color,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: Theme.of(context).hintColor,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: color.withValues(alpha: 0.6),
            ),
          ],
        ),
      ),
    );
  }

  Future<String?> _inputUrl() async {
    TextEditingController urlController = TextEditingController();

    return await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Enter the URL'),
        content: TextField(
          controller: urlController,
          autofocus: true,
          decoration: InputDecoration(labelText: 'URL'),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context, null);
            },
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context, urlController.text);
            },
            child: Text('Add'),
          ),
        ],
      ),
    );
  }

  Future<void> _processFileOrUrl(
    String firebaseUrl, {
    required bool isUrl,
  }) async {
    String userId = _auth.currentUser!.uid;
    String? fileName = isUrl
        ? firebaseUrl
        : Util_File.parseFirebaseUrl(firebaseUrl)["document_name"];
    String fullPath = '${_currentPath ?? "/"}';
    if (!fullPath.endsWith("/")) {
      fullPath += "/";
    }
    if (!fullPath.startsWith('/')) {
      fullPath = "/" + fullPath;
    }

    final newFile = File(
      userId,
      fileName ?? "",
      "File",
      path: fullPath,
      depth: fullPath.split("/").length - 1,
      firebaseUrl: firebaseUrl,
    );

    await _fileSystemRepository.createFileSystemItem(userId, fullPath, newFile);

    await _listFiles(folderPath: fullPath);
    await _searchClient.embedDocument([firebaseUrl], userId, true);
  }

  Future<void> _uploadFile() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      withData: true,
    );

    if (result != null) {
      // by default, add to current path
      String? folderPath = _currentPath;
      folderPath = folderPath ?? '/';
      if (!folderPath.startsWith("/")) {
        folderPath = "/$folderPath";
      }

      final String virtualParentPath = folderPath.endsWith("/")
          ? folderPath
          : '$folderPath/';
      PlatformFile file = result.files.single;
      String? firebaseUrl = await _firebaseStorageRepository
          .uploadFileToFirebase(
            virtualParentPath,
            file.name,
            file.bytes!,
            kIsWeb ? "" : file.path!,
          );
      await _processFileOrUrl(firebaseUrl, isUrl: false);
    }
  }

  Future<bool> _onWillPop() async {
    logger.d("storage page on will pop called");
    Navigator.pop(context, widget.selectedFilesNotifier?.value);
    return true;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return PopScope(
      onPopInvoked: (didPop) {
        if (didPop) {
          logger.d("storage page on pop invoked");
          // Handle any cleanup if needed
        }
      },
      child: ScaffoldMessenger(
        child: Scaffold(
          backgroundColor: isDark
              ? AppTheme.darkSurface
              : AppTheme.lightSurface,
          appBar: AppBarStyles.primary(
            title: 'Files & Documents',
            actions: [
              // Search toggle button
              IconButton(
                icon: Icon(Icons.search, color: Colors.white),
                tooltip: 'Search files',
                onPressed: () {
                  // TODO: Implement search functionality
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Search functionality coming soon!'),
                    ),
                  );
                },
              ),
              // PDF Sessions history
              IconButton(
                icon: Icon(Icons.history, color: Colors.white),
                tooltip: 'PDF Sessions History',
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => PdfSessionsListPage(),
                    ),
                  );
                },
              ),
            ],
          ),
          body: Column(
            children: [
              // Enhanced file list with loading state
              Expanded(
                child: _isLoading
                    ? Center(
                        child: LoadingStyles.primary(
                          message: 'Loading files...',
                        ),
                      )
                    : ValueListenableBuilder<List<FileSystemItem>>(
                        valueListenable: _fileSystemItems,
                        builder: (context, value, child) {
                          if (value.isEmpty) {
                            return _buildEmptyState(context, theme);
                          }

                          return FileSelectionWidget(
                            fileSystemItems: value,
                            preselectedItems: widget.preselectedItems,
                            selectedFilesNotifier: widget.selectedFilesNotifier,
                            onPathChange: (newPath) {
                              setState(() {
                                _currentPath = newPath; // Update current path
                              });
                            },
                          );
                        },
                      ),
              ),

              // Enhanced upload section
              _buildUploadSection(context, theme, isDark),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds an enhanced empty state widget with helpful messaging
  Widget _buildEmptyState(BuildContext context, ThemeData theme) {
    return Center(
      child: CardStyles.outlined(
        margin: const EdgeInsets.all(32),
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.folder_open,
              size: 64,
              color: AppTheme.primaryGreen.withValues(alpha: 0.6),
            ),
            const SizedBox(height: 16),
            Text(
              'No files found',
              style: theme.textTheme.headlineSmall?.copyWith(
                color: AppTheme.primaryGreen,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _currentPath.isEmpty || _currentPath == '/'
                  ? 'Start by uploading your first file or adding a URL'
                  : 'This folder is empty. Add some files to get started.',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.hintColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ButtonStyles.primary(
              text: 'Add Files',
              icon: Icons.add,
              onPressed: _uploadFileOrUrl,
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the enhanced upload section with better styling
  Widget _buildUploadSection(
    BuildContext context,
    ThemeData theme,
    bool isDark,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: isDark ? AppTheme.darkSurface : AppTheme.lightSurface,
        border: Border(
          top: BorderSide(
            color: isDark ? AppTheme.darkOutline : AppTheme.lightOutline,
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              // Current path indicator
              Expanded(
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    color: theme.cardColor,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: AppTheme.primaryGreen.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.folder,
                        size: 16,
                        color: AppTheme.primaryGreen,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _currentPath.isEmpty ? '/' : _currentPath,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: AppTheme.primaryGreen,
                            fontWeight: FontWeight.w500,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(width: 16),

              // Upload button
              _isUploading
                  ? Container(
                      width: 56,
                      height: 56,
                      decoration: BoxDecoration(
                        color: AppTheme.primaryGreen.withValues(alpha: 0.1),
                        shape: BoxShape.circle,
                      ),
                      child: LoadingStyles.primary(size: 24),
                    )
                  : FloatingActionButton(
                      onPressed: _uploadFileOrUrl,
                      backgroundColor: AppTheme.primaryGreen,
                      foregroundColor: Colors.white,
                      tooltip: 'Upload File or Add URL',
                      child: const Icon(Icons.add),
                    ),
            ],
          ),
        ),
      ),
    );
  }
}
